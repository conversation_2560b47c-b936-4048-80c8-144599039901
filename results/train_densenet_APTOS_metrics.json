{"timestamp": "2025-08-22T22:32:30.542922", "test_results": {"accuracy": 0.7158469945355191, "qwk": 0.662833112983641, "auc": 0.8690658198620689, "precision": 0.5722415770762533, "recall": 0.7158469945355191, "f1": 0.6345639197222924}, "training_history": {"train_loss": [1.2990164555933164, 1.1171918660402298, 1.037913794102876, 0.9717590446057527, 0.9318742013495901, 0.9231269877889882, 0.8911008724699849, 0.8800080703652423, 0.8769606371288714, 0.873940519016722], "val_accuracy": [0.4890710382513661, 0.5163934426229508, 0.6092896174863388, 0.6530054644808743, 0.6202185792349727, 0.674863387978142, 0.674863387978142, 0.6693989071038251, 0.6775956284153005, 0.6721311475409836], "val_qwk": [0.048216585307418236, 0.18953513119090326, 0.478267781628565, 0.5655634687892752, 0.5116264233995726, 0.6176480140300737, 0.606017331472526, 0.5986191695569025, 0.6330200643881501, 0.6023158509292594], "val_auc": [0.6629841171627472, 0.7610929978209903, 0.7972747855270671, 0.8214353658125194, 0.8359273500686417, 0.840154484420015, 0.847836632648385, 0.8488430632235786, 0.8491397348759468, 0.8490845840560229], "val_precision": [0.4085164739055296, 0.37912457912457914, 0.44828981987451927, 0.48886057718344644, 0.4584502388954291, 0.5103189551602527, 0.5098326114139258, 0.5048309178743962, 0.5133671494033178, 0.5073316662622815], "val_recall": [0.4890710382513661, 0.5163934426229508, 0.6092896174863388, 0.6530054644808743, 0.6202185792349727, 0.674863387978142, 0.674863387978142, 0.6693989071038251, 0.6775956284153005, 0.6721311475409836], "val_f1": [0.3413500391994565, 0.39104285434374914, 0.5131663058047221, 0.5588191900031142, 0.5248274074304496, 0.5811304197113457, 0.5808357383081312, 0.5755828026915955, 0.5840536181707641, 0.5782133942425381], "learning_rate": [0.0001, 9.755282581475769e-05, 9.045084971874737e-05, 7.938926261462366e-05, 6.545084971874737e-05, 4.9999999999999996e-05, 3.454915028125263e-05, 2.0610737385376345e-05, 9.549150281252631e-06, 2.447174185242323e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "best_epoch": 9, "best_qwk": 0.6330200643881501}, "training_config": {"model": "DenseNet121", "num_classes": 5, "epochs": 10, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss"}}