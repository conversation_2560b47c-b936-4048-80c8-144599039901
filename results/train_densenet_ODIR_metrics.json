{"timestamp": "2025-08-22T22:38:57.755872", "test_results": {"accuracy": 0.6721611721611722, "auc": 0.7260931986206712, "precision": 0.6721819645732688, "recall": 0.6721611721611722, "f1": 0.6721512745423623}, "training_history": {"train_loss": [0.7183184540724452, 0.6876222536533694, 0.6680789461618737, 0.659099821802936, 0.6423724957659275, 0.6423678217054922, 0.6318346292157716, 0.6328524034234542, 0.6342539191246033, 0.6249875139586533], "val_accuracy": [0.5183150183150184, 0.5769230769230769, 0.5989010989010989, 0.5970695970695971, 0.6501831501831502, 0.6410256410256411, 0.6501831501831502, 0.6446886446886447, 0.6446886446886447, 0.6446886446886447], "val_auc": [0.5270968347891425, 0.6021817010828, 0.6442324464302487, 0.6665727434958204, 0.6785814917683048, 0.6827409464772102, 0.6867125548444231, 0.6886581062405239, 0.689275315648942, 0.6884836774946664], "val_precision": [0.5255121112440192, 0.5769736842105263, 0.6052398629354655, 0.6089057489951677, 0.6502335445076775, 0.6410559231275078, 0.6598086807538549, 0.6458155855746217, 0.6478629900314005, 0.6485473806014355], "val_recall": [0.5183150183150184, 0.5769230769230769, 0.5989010989010989, 0.5970695970695971, 0.6501831501831502, 0.6410256410256411, 0.6501831501831502, 0.6446886446886447, 0.6446886446886447, 0.6446886446886447], "val_f1": [0.48176591805580116, 0.5768535262206148, 0.592769057341457, 0.5858159549817942, 0.6501538120909387, 0.6410063737001007, 0.6448351139370698, 0.6440008066142368, 0.6427713850180091, 0.6423661287055169], "learning_rate": [0.0001, 9.755282581475769e-05, 9.045084971874737e-05, 7.938926261462366e-05, 6.545084971874737e-05, 4.9999999999999996e-05, 3.454915028125263e-05, 2.0610737385376345e-05, 9.549150281252631e-06, 2.447174185242323e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "best_epoch": 9, "best_auc": 0.689275315648942}, "training_config": {"model": "DenseNet121", "num_classes": 2, "epochs": 10, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss"}}