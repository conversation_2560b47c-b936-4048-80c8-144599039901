#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取消融实验数据脚本
从results文件夹中的JSON文件提取关键性能指标，用于生成消融实验表格
"""

import json
import os
from pathlib import Path

def extract_metrics_from_json(file_path):
    """从JSON文件中提取关键性能指标"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    test_results = data.get('test_results', {})
    training_config = data.get('training_config', {})
    
    return {
        'model': training_config.get('model', 'Unknown'),
        'accuracy': test_results.get('accuracy', 0.0),
        'qwk': test_results.get('qwk', 0.0),
        'auc': test_results.get('auc', 0.0),
        'precision': test_results.get('precision', 0.0),
        'recall': test_results.get('recall', 0.0),
        'f1': test_results.get('f1', 0.0),
        'config': training_config
    }

def main():
    """主函数：提取所有模型的性能数据"""
    results_dir = Path('results')
    
    # 定义模型文件映射
    model_files = {
        'Baseline (ResNet50)': 'train_baseline_metrics.json',
        'ResNet50 + EMA': 'train_resnet50_ema_V2_metrics.json', 
        'ResNet50 + PPM': 'train_resnet50_ppm_metrics.json',
        'ResNet50 + EMA + PPM (方案A)': 'train_resnet50_ema_ppm_A_metrics.json',
        'ResNet50 + EMA + PPM (方案D)': 'train_resnet50_ema_ppm_D_metrics.json',
        'DenseNet121 (对比)': 'train_densenet_APTOS_metrics.json'
    }
    
    print("APTOS 2019 数据集消融实验结果")
    print("=" * 80)
    print(f"{'模型配置':<30} {'Acc':<8} {'QWK':<8} {'AUC':<8} {'Prec':<8} {'Rec':<8} {'F1':<8}")
    print("-" * 80)
    
    results = {}
    
    for model_name, file_name in model_files.items():
        file_path = results_dir / file_name
        if file_path.exists():
            metrics = extract_metrics_from_json(file_path)
            results[model_name] = metrics
            
            print(f"{model_name:<30} "
                  f"{metrics['accuracy']:<8.4f} "
                  f"{metrics['qwk']:<8.4f} "
                  f"{metrics['auc']:<8.4f} "
                  f"{metrics['precision']:<8.4f} "
                  f"{metrics['recall']:<8.4f} "
                  f"{metrics['f1']:<8.4f}")
        else:
            print(f"文件不存在: {file_path}")
    
    print("\n" + "=" * 80)
    
    # 计算改进幅度
    if 'Baseline (ResNet50)' in results:
        baseline = results['Baseline (ResNet50)']
        print("\n相对于基线的改进幅度 (百分点):")
        print("-" * 80)
        print(f"{'模型配置':<30} {'Acc':<8} {'QWK':<8} {'AUC':<8} {'Prec':<8} {'Rec':<8} {'F1':<8}")
        print("-" * 80)
        
        for model_name, metrics in results.items():
            if model_name != 'Baseline (ResNet50)':
                acc_imp = (metrics['accuracy'] - baseline['accuracy']) * 100
                qwk_imp = (metrics['qwk'] - baseline['qwk']) * 100
                auc_imp = (metrics['auc'] - baseline['auc']) * 100
                prec_imp = (metrics['precision'] - baseline['precision']) * 100
                rec_imp = (metrics['recall'] - baseline['recall']) * 100
                f1_imp = (metrics['f1'] - baseline['f1']) * 100
                
                print(f"{model_name:<30} "
                      f"{acc_imp:<8.2f} "
                      f"{qwk_imp:<8.2f} "
                      f"{auc_imp:<8.2f} "
                      f"{prec_imp:<8.2f} "
                      f"{rec_imp:<8.2f} "
                      f"{f1_imp:<8.2f}")
    
    return results

if __name__ == "__main__":
    results = main()
